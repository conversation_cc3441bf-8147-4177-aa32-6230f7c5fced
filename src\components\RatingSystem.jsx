import { useState, useEffect } from 'react';
import axios from 'axios';

const API_URL = 'http://localhost:3001/api';

function RatingSystem({ user, onClose }) {
  const [pendingTrips, setPendingTrips] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    fetchPendingRatings();
  }, []);

  const fetchPendingRatings = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/rating/pending`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setPendingTrips(response.data);
    } catch (error) {
      console.error('Error fetching pending ratings:', error);
    } finally {
      setLoading(false);
    }
  };

  const submitRating = async (tripId, ratedUserId, rating, comment, ratingType) => {
    setSubmitting(true);
    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/rating/submit`, {
        tripId,
        ratedUserId,
        rating,
        comment,
        ratingType
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Remove the trip from pending list
      setPendingTrips(prev => prev.filter(trip => trip.id !== tripId));
      alert('¡Calificación enviada exitosamente!');
    } catch (error) {
      alert('Error al enviar calificación: ' + (error.response?.data?.error || 'Error desconocido'));
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="rating-overlay">
        <div className="rating-container">
          <div className="loading">
            <div className="spinner"></div>
            <p>Cargando viajes pendientes...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="rating-overlay">
      <div className="rating-container">
        <div className="rating-header">
          <h2>⭐ Calificar Viajes</h2>
          <button onClick={onClose} className="close-btn">✕</button>
        </div>

        {pendingTrips.length === 0 ? (
          <div className="no-pending-ratings">
            <p>🎉 ¡No tienes viajes pendientes por calificar!</p>
            <button onClick={onClose} className="btn-primary">Cerrar</button>
          </div>
        ) : (
          <div className="pending-trips">
            <p>Tienes {pendingTrips.length} viaje(s) pendiente(s) por calificar:</p>
            
            {pendingTrips.map(trip => (
              <TripRatingCard
                key={trip.id}
                trip={trip}
                onSubmitRating={submitRating}
                submitting={submitting}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

function TripRatingCard({ trip, onSubmitRating, submitting }) {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [hoveredRating, setHoveredRating] = useState(0);

  const handleSubmit = () => {
    if (rating === 0) {
      alert('Por favor selecciona una calificación');
      return;
    }

    const ratingType = trip.user_role === 'driver' ? 'driver_to_passenger' : 'passenger_to_driver';
    onSubmitRating(trip.id, trip.other_user_id, rating, comment, ratingType);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="trip-rating-card">
      <div className="trip-info">
        <h4>
          {trip.user_role === 'driver' ? '👤 Pasajero:' : '🏍️ Conductor:'} {trip.other_user_name}
        </h4>
        <p><strong>💰 Precio:</strong> ${trip.final_price}</p>
        <p><strong>📅 Fecha:</strong> {formatDate(trip.completed_at)}</p>
      </div>

      <div className="rating-section">
        <h5>¿Cómo fue tu experiencia?</h5>
        
        <div className="star-rating">
          {[1, 2, 3, 4, 5].map(star => (
            <button
              key={star}
              className={`star ${star <= (hoveredRating || rating) ? 'active' : ''}`}
              onClick={() => setRating(star)}
              onMouseEnter={() => setHoveredRating(star)}
              onMouseLeave={() => setHoveredRating(0)}
              disabled={submitting}
            >
              ⭐
            </button>
          ))}
        </div>

        <div className="rating-labels">
          {rating === 1 && <span className="rating-label bad">😞 Muy malo</span>}
          {rating === 2 && <span className="rating-label poor">😐 Malo</span>}
          {rating === 3 && <span className="rating-label average">🙂 Regular</span>}
          {rating === 4 && <span className="rating-label good">😊 Bueno</span>}
          {rating === 5 && <span className="rating-label excellent">🤩 Excelente</span>}
        </div>

        <div className="comment-section">
          <label htmlFor={`comment-${trip.id}`}>Comentario (opcional):</label>
          <textarea
            id={`comment-${trip.id}`}
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Comparte tu experiencia..."
            maxLength="200"
            disabled={submitting}
          />
          <small>{comment.length}/200 caracteres</small>
        </div>

        <button
          onClick={handleSubmit}
          disabled={rating === 0 || submitting}
          className="btn-primary submit-rating-btn"
        >
          {submitting ? 'Enviando...' : '✅ Enviar Calificación'}
        </button>
      </div>
    </div>
  );
}

function UserRatings({ userId, userName }) {
  const [ratings, setRatings] = useState([]);
  const [stats, setStats] = useState({ average: 0, total: 0 });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUserRatings();
  }, [userId]);

  const fetchUserRatings = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/rating/user/${userId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setRatings(response.data.ratings);
      setStats({ average: response.data.average, total: response.data.total });
    } catch (error) {
      console.error('Error fetching user ratings:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`star ${i < rating ? 'filled' : ''}`}>
        ⭐
      </span>
    ));
  };

  if (loading) {
    return <div className="loading">Cargando calificaciones...</div>;
  }

  return (
    <div className="user-ratings">
      <div className="ratings-header">
        <h3>⭐ Calificaciones de {userName}</h3>
        <div className="rating-summary">
          <div className="average-rating">
            <span className="rating-number">{stats.average.toFixed(1)}</span>
            <div className="stars">{renderStars(Math.round(stats.average))}</div>
            <span className="total-ratings">({stats.total} calificaciones)</span>
          </div>
        </div>
      </div>

      {ratings.length === 0 ? (
        <p>No hay calificaciones disponibles</p>
      ) : (
        <div className="ratings-list">
          {ratings.map(rating => (
            <div key={rating.id} className="rating-item">
              <div className="rating-header">
                <span className="rater-name">{rating.rater_name}</span>
                <div className="stars">{renderStars(rating.rating)}</div>
                <span className="rating-date">
                  {new Date(rating.created_at).toLocaleDateString('es-ES')}
                </span>
              </div>
              {rating.comment && (
                <p className="rating-comment">"{rating.comment}"</p>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export { RatingSystem, UserRatings };
export default RatingSystem;

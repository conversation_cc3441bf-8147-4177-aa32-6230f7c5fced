import { useRef, useEffect, useState } from 'react';

function CameraCapture({ onCapture, onCancel }) {
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const [stream, setStream] = useState(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    startCamera();
    return () => {
      stopCamera();
    };
  }, []);

  const startCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { 
          facingMode: 'user', // Cámara frontal
          width: { ideal: 640 },
          height: { ideal: 480 }
        }
      });
      
      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        videoRef.current.onloadedmetadata = () => {
          setIsReady(true);
        };
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('❌ No se pudo acceder a la cámara. Verifica los permisos en tu navegador.');
      onCancel();
    }
  };

  const stopCamera = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current || !isReady) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    // Configurar el canvas con las dimensiones del video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Dibujar el frame actual del video en el canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convertir a base64
    const photoDataUrl = canvas.toDataURL('image/jpeg', 0.8);
    
    // Detener la cámara
    stopCamera();
    
    // Enviar la foto capturada
    onCapture(photoDataUrl);
  };

  const handleCancel = () => {
    stopCamera();
    onCancel();
  };

  return (
    <div className="camera-capture-overlay">
      <div className="camera-capture-container">
        <div className="camera-header">
          <h3>📸 Verificación de Identidad</h3>
          <p>Tómate una selfie para verificar tu identidad antes del viaje</p>
        </div>

        <div className="camera-preview">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="camera-video"
          />
          <canvas
            ref={canvasRef}
            style={{ display: 'none' }}
          />
          
          {!isReady && (
            <div className="camera-loading">
              <div className="spinner"></div>
              <p>Iniciando cámara...</p>
            </div>
          )}
        </div>

        <div className="camera-instructions">
          <p>• Asegúrate de que tu rostro esté bien iluminado</p>
          <p>• Mira directamente a la cámara</p>
          <p>• Mantén el teléfono estable</p>
        </div>

        <div className="camera-controls">
          <button 
            onClick={handleCancel}
            className="btn-secondary"
          >
            ❌ Cancelar
          </button>
          <button 
            onClick={capturePhoto}
            disabled={!isReady}
            className="btn-primary capture-btn"
          >
            📸 Capturar Foto
          </button>
        </div>
      </div>
    </div>
  );
}

export default CameraCapture;

function PhotoConfirmation({ photo, onConfirm, onRetake }) {
  return (
    <div className="photo-confirmation-overlay">
      <div className="photo-confirmation-container">
        <div className="photo-header">
          <h3>✅ Foto Capturada</h3>
          <p>Confirma que la foto se ve bien antes de continuar</p>
        </div>

        <div className="photo-preview">
          <img 
            src={photo} 
            alt="Foto de verificación" 
            className="captured-photo"
          />
        </div>

        <div className="photo-quality-check">
          <p><strong>Verifica que:</strong></p>
          <ul>
            <li>✓ Tu rostro se ve claramente</li>
            <li>✓ La foto no está borrosa</li>
            <li>✓ Hay buena iluminación</li>
            <li>✓ Eres tú quien aparece en la foto</li>
          </ul>
        </div>

        <div className="photo-controls">
          <button 
            onClick={onRetake}
            className="btn-secondary"
          >
            🔄 Tomar Otra Foto
          </button>
          <button 
            onClick={onConfirm}
            className="btn-primary"
          >
            ✅ Confirmar Foto
          </button>
        </div>

        <div className="security-notice">
          <p><small>
            🔒 Tu foto se usa solo para verificación de seguridad y no se comparte con terceros.
          </small></p>
        </div>
      </div>
    </div>
  );
}

export default PhotoConfirmation;

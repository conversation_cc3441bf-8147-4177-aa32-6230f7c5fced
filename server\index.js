import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import mongoose from 'mongoose';

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: "http://localhost:5173",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors());
app.use(express.json());

// MongoDB Connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb+srv://motoexpress:<EMAIL>/motoexpress?retryWrites=true&w=majority';

// Configure mongoose
mongoose.set('strictQuery', false);

mongoose.connect(MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('🍃 Connected to MongoDB Atlas'))
.catch(err => {
  console.error('❌ MongoDB connection error:', err);
  console.log('💡 Trying to connect to MongoDB Atlas...');

  // Fallback to local MongoDB
  const localURI = 'mongodb://localhost:27017/motoexpress';
  return mongoose.connect(localURI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
})
.then(() => console.log('🍃 Connected to local MongoDB'))
.catch(err => {
  console.error('❌ All MongoDB connections failed:', err);
  console.log('⚠️  Server running without database - some features may not work');
});

// MongoDB Schemas
const userSchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  phone: String,
  user_type: { 
    type: String, 
    required: true, 
    enum: ['driver', 'passenger', 'both'] 
  },
  passenger_rating: { type: Number, default: 0 },
  passenger_total_ratings: { type: Number, default: 0 },
  created_at: { type: Date, default: Date.now }
});

const driverSchema = new mongoose.Schema({
  user_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
  current_balance: { type: Number, default: 0 },
  total_earnings: { type: Number, default: 0 },
  is_active: { type: Boolean, default: false },
  current_location: String,
  rating_average: { type: Number, default: 0 },
  total_ratings: { type: Number, default: 0 },
  total_trips: { type: Number, default: 0 },
  last_recharge: Date,
  last_seen: { type: Date, default: Date.now }
});

const rechargeSchema = new mongoose.Schema({
  driver_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Driver', required: true },
  amount: { type: Number, required: true },
  yappy_transaction_id: String,
  status: { type: String, default: 'pending' },
  created_at: { type: Date, default: Date.now }
});

const tripRequestSchema = new mongoose.Schema({
  passenger_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  pickup_location: { type: String, required: true },
  destination: { type: String, required: true },
  initial_price: { type: Number, required: true },
  current_price: { type: Number, required: true },
  standard_price: Number,
  price_increase: { type: Number, default: 0 },
  estimated_distance: Number,
  price_increases: { type: Number, default: 0 },
  status: { type: String, default: 'pending' },
  payment_method: { 
    type: String, 
    enum: ['yappy', 'cash'] 
  },
  created_at: { type: Date, default: Date.now }
});

const priceNegotiationSchema = new mongoose.Schema({
  trip_request_id: { type: mongoose.Schema.Types.ObjectId, ref: 'TripRequest', required: true },
  driver_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Driver', required: true },
  offered_price: { type: Number, required: true },
  status: { type: String, default: 'pending' },
  created_at: { type: Date, default: Date.now }
});

const tripSchema = new mongoose.Schema({
  trip_request_id: { type: mongoose.Schema.Types.ObjectId, ref: 'TripRequest', required: true },
  driver_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Driver', required: true },
  passenger_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  final_price: { type: Number, required: true },
  commission: { type: Number, required: true },
  driver_earnings: { type: Number, required: true },
  payment_method: { type: String, required: true },
  status: { type: String, default: 'completed' },
  started_at: Date,
  completed_at: { type: Date, default: Date.now }
});

const ratingSchema = new mongoose.Schema({
  trip_id: { type: mongoose.Schema.Types.ObjectId, ref: 'Trip', required: true },
  rater_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  rated_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  rating: { 
    type: Number, 
    required: true, 
    min: 1, 
    max: 5 
  },
  comment: String,
  rating_type: { 
    type: String, 
    required: true, 
    enum: ['driver_to_passenger', 'passenger_to_driver'] 
  },
  created_at: { type: Date, default: Date.now }
});

const activeUserSchema = new mongoose.Schema({
  user_id: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, unique: true },
  user_type: { type: String, required: true },
  current_location: String,
  is_online: { type: Boolean, default: true },
  last_seen: { type: Date, default: Date.now }
});

// MongoDB Models
const User = mongoose.model('User', userSchema);
const Driver = mongoose.model('Driver', driverSchema);
const Recharge = mongoose.model('Recharge', rechargeSchema);
const TripRequest = mongoose.model('TripRequest', tripRequestSchema);
const PriceNegotiation = mongoose.model('PriceNegotiation', priceNegotiationSchema);
const Trip = mongoose.model('Trip', tripSchema);
const Rating = mongoose.model('Rating', ratingSchema);
const ActiveUser = mongoose.model('ActiveUser', activeUserSchema);

// JWT Secret
const JWT_SECRET = 'motoexpress_secret_key_2024';

// Auth middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' });
    }
    req.user = user;
    next();
  });
};

// Routes

// Register
app.post('/api/register', async (req, res) => {
  try {
    const { name, email, password, phone, userType } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: 'Email already exists' });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user
    const user = new User({
      name,
      email,
      password: hashedPassword,
      phone,
      user_type: userType
    });
    
    const savedUser = await user.save();
    const userId = savedUser._id;
    
    // If driver or both, create driver record
    if (userType === 'driver' || userType === 'both') {
      const driver = new Driver({
        user_id: userId
      });
      await driver.save();
    }
    
    const token = jwt.sign({ userId, userType }, JWT_SECRET);
    res.json({ token, userId, userType, message: 'Usuario registrado exitosamente' });
    
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Login
app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }
    
    const validPassword = await bcrypt.compare(password, user.password);
    if (!validPassword) {
      return res.status(400).json({ error: 'Invalid credentials' });
    }
    
    const token = jwt.sign({ userId: user._id, userType: user.user_type }, JWT_SECRET);
    res.json({ 
      token, 
      userId: user._id, 
      userType: user.user_type,
      name: user.name,
      message: 'Login successful' 
    });
    
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Driver routes

// Create driver profile (when user switches to driver mode)
app.post('/api/driver/create-profile', authenticateToken, async (req, res) => {
  try {
    const existingDriver = await Driver.findOne({ user_id: req.user.userId });
    
    if (existingDriver) {
      return res.json({ message: 'Driver profile already exists' });
    }

    const driver = new Driver({
      user_id: req.user.userId
    });
    
    const savedDriver = await driver.save();
    
    res.json({
      message: 'Driver profile created successfully',
      driverId: savedDriver._id
    });
    
  } catch (error) {
    console.error('Create driver profile error:', error);
    res.status(500).json({ error: 'Failed to create driver profile' });
  }
});

// Get driver dashboard data
app.get('/api/driver/dashboard', authenticateToken, async (req, res) => {
  try {
    const driver = await Driver.findOne({ user_id: req.user.userId }).populate('user_id', 'name');

    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    const driverData = {
      ...driver.toObject(),
      name: driver.user_id.name
    };

    res.json(driverData);

  } catch (error) {
    console.error('Driver dashboard error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Recharge driver account (Yappy simulation)
app.post('/api/driver/recharge', authenticateToken, async (req, res) => {
  try {
    const { yappyTransactionId } = req.body;
    const rechargeAmount = 10; // Fixed $10 recharge
    const creditAmount = 100; // $100 credit for platform use

    // Get driver
    const driver = await Driver.findOne({ user_id: req.user.userId });
    if (!driver) {
      return res.status(404).json({ error: 'Driver not found' });
    }

    // Record recharge
    const recharge = new Recharge({
      driver_id: driver._id,
      amount: rechargeAmount,
      yappy_transaction_id: yappyTransactionId || 'YAPPY_' + Date.now(),
      status: 'completed'
    });
    await recharge.save();

    // Update driver balance and activate
    driver.current_balance = creditAmount;
    driver.is_active = true;
    driver.last_recharge = new Date();
    await driver.save();

    res.json({
      message: 'Recharge successful',
      newBalance: creditAmount,
      isActive: true
    });

  } catch (error) {
    console.error('Recharge error:', error);
    res.status(500).json({ error: 'Recharge failed' });
  }
});

// Trip request routes

// Create trip request (passenger)
app.post('/api/trip/request', authenticateToken, async (req, res) => {
  try {
    const {
      pickupLocation,
      destination,
      initialPrice,
      paymentMethod,
      estimatedDistance,
      standardPrice,
      priceIncrease
    } = req.body;

    const tripRequest = new TripRequest({
      passenger_id: req.user.userId,
      pickup_location: pickupLocation,
      destination: destination,
      initial_price: initialPrice,
      current_price: initialPrice,
      standard_price: standardPrice,
      price_increase: priceIncrease || 0,
      estimated_distance: estimatedDistance,
      payment_method: paymentMethod
    });

    const savedTripRequest = await tripRequest.save();

    // Emit to all active drivers
    io.emit('new_trip_request', {
      id: savedTripRequest._id,
      pickupLocation,
      destination,
      currentPrice: initialPrice,
      paymentMethod
    });

    res.json({
      message: 'Trip request created',
      tripRequestId: savedTripRequest._id,
      status: 'pending'
    });

  } catch (error) {
    console.error('Trip request error:', error);
    res.status(500).json({ error: 'Trip request failed' });
  }
});

// Get active trip requests (for drivers)
app.get('/api/trip/requests', authenticateToken, async (req, res) => {
  try {
    if (req.user.userType !== 'driver') {
      return res.status(403).json({ error: 'Access denied' });
    }

    const requests = await TripRequest.find({ status: 'pending' })
      .populate('passenger_id', 'name phone')
      .sort({ created_at: -1 });

    const formattedRequests = requests.map(request => ({
      ...request.toObject(),
      passenger_name: request.passenger_id.name,
      passenger_phone: request.passenger_id.phone
    }));

    res.json(formattedRequests);

  } catch (error) {
    console.error('Get trip requests error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Increase trip price (passenger - max 3 times)
app.post('/api/trip/:id/increase-price', authenticateToken, async (req, res) => {
  try {
    if (req.user.userType !== 'passenger') {
      return res.status(403).json({ error: 'Access denied' });
    }

    const { newPrice } = req.body;
    const tripId = req.params.id;

    const trip = await TripRequest.findOne({
      _id: tripId,
      passenger_id: req.user.userId,
      status: 'pending'
    });

    if (!trip) {
      return res.status(404).json({ error: 'Trip not found' });
    }

    if (trip.price_increases >= 3) {
      return res.status(400).json({ error: 'Maximum price increases reached' });
    }

    trip.current_price = newPrice;
    trip.price_increases += 1;
    await trip.save();

    // Emit price update to drivers
    io.emit('trip_price_updated', {
      tripId,
      newPrice,
      priceIncreases: trip.price_increases
    });

    res.json({ message: 'Price updated successfully', newPrice });

  } catch (error) {
    console.error('Price increase error:', error);
    res.status(500).json({ error: 'Price update failed' });
  }
});

// Driver counter-offer
app.post('/api/trip/:id/counter-offer', authenticateToken, async (req, res) => {
  try {
    if (req.user.userType !== 'driver') {
      return res.status(403).json({ error: 'Access denied' });
    }

    const { offeredPrice } = req.body;
    const tripId = req.params.id;

    // Get driver
    const driver = await Driver.findOne({ user_id: req.user.userId, is_active: true });
    if (!driver) {
      return res.status(400).json({ error: 'Driver not active or not found' });
    }

    if (driver.current_balance <= 0) {
      return res.status(400).json({ error: 'Insufficient balance. Please recharge.' });
    }

    const priceNegotiation = new PriceNegotiation({
      trip_request_id: tripId,
      driver_id: driver._id,
      offered_price: offeredPrice
    });

    const savedNegotiation = await priceNegotiation.save();

    // Emit counter-offer to passenger
    io.emit('counter_offer_received', {
      tripId,
      driverId: driver._id,
      offeredPrice,
      negotiationId: savedNegotiation._id
    });

    res.json({ message: 'Counter-offer sent successfully' });

  } catch (error) {
    console.error('Counter-offer error:', error);
    res.status(500).json({ error: 'Counter-offer failed' });
  }
});

// Accept trip (driver accepts passenger's price)
app.post('/api/trip/:id/accept', authenticateToken, async (req, res) => {
  try {
    const tripId = req.params.id;

    if (req.user.userType === 'driver') {
      // Driver accepting passenger's original price
      const driver = await Driver.findOne({
        user_id: req.user.userId,
        is_active: true
      });

      if (!driver || driver.current_balance <= 0) {
        return res.status(400).json({ error: 'Driver not found, inactive, or insufficient balance' });
      }

      // Get trip details
      const trip = await TripRequest.findOne({ _id: tripId, status: 'pending' });
      if (!trip) {
        return res.status(404).json({ error: 'Trip not found' });
      }

      const commission = 1.00; // Fixed $1 commission per trip
      const driverEarnings = trip.current_price; // Driver gets full trip price
      const newBalance = driver.current_balance - commission; // Deduct $1 from balance

      // Create completed trip
      const completedTrip = new Trip({
        trip_request_id: tripId,
        driver_id: driver._id,
        passenger_id: trip.passenger_id,
        final_price: trip.current_price,
        commission: commission,
        driver_earnings: driverEarnings,
        payment_method: trip.payment_method
      });

      await completedTrip.save();

      // Update trip request status
      trip.status = 'accepted';
      await trip.save();

      // Update driver balance and earnings
      driver.current_balance = newBalance;
      driver.total_earnings += driverEarnings;
      driver.is_active = newBalance > 0;
      driver.total_trips += 1;
      await driver.save();

      // Emit trip accepted
      io.emit('trip_accepted', {
        tripId,
        driverId: driver._id,
        finalPrice: trip.current_price,
        paymentMethod: trip.payment_method
      });

      res.json({
        message: 'Trip accepted successfully',
        finalPrice: trip.current_price,
        commission,
        earnings: driverEarnings,
        newBalance,
        isActive: newBalance > 0
      });
    }

  } catch (error) {
    console.error('Accept trip error:', error);
    res.status(500).json({ error: 'Trip acceptance failed' });
  }
});

// Rating routes

// Submit rating after trip
app.post('/api/rating/submit', authenticateToken, async (req, res) => {
  try {
    const { tripId, ratedUserId, rating, comment, ratingType } = req.body;

    // Validate rating
    if (rating < 1 || rating > 5) {
      return res.status(400).json({ error: 'Rating must be between 1 and 5' });
    }

    // Check if rating already exists
    const existingRating = await Rating.findOne({
      trip_id: tripId,
      rater_id: req.user.userId,
      rating_type: ratingType
    });

    if (existingRating) {
      return res.status(400).json({ error: 'Ya has calificado este viaje' });
    }

    // Insert rating
    const newRating = new Rating({
      trip_id: tripId,
      rater_id: req.user.userId,
      rated_id: ratedUserId,
      rating: rating,
      comment: comment,
      rating_type: ratingType
    });

    const savedRating = await newRating.save();

    // Update average rating
    await updateUserRating(ratedUserId, ratingType);

    res.json({
      message: 'Calificación enviada exitosamente',
      ratingId: savedRating._id
    });

  } catch (error) {
    console.error('Submit rating error:', error);
    res.status(500).json({ error: 'Failed to submit rating' });
  }
});

// Get user ratings
app.get('/api/rating/user/:userId', authenticateToken, async (req, res) => {
  try {
    const userId = req.params.userId;

    const ratings = await Rating.find({ rated_id: userId })
      .populate('rater_id', 'name')
      .populate('trip_id')
      .sort({ created_at: -1 })
      .limit(20);

    // Get average rating
    const ratingStats = await Rating.aggregate([
      { $match: { rated_id: new mongoose.Types.ObjectId(userId) } },
      { $group: { _id: null, average: { $avg: '$rating' }, total: { $sum: 1 } } }
    ]);

    const stats = ratingStats[0] || { average: 0, total: 0 };

    res.json({
      ratings: ratings.map(r => ({
        ...r.toObject(),
        rater_name: r.rater_id.name,
        final_price: r.trip_id?.final_price,
        completed_at: r.trip_id?.completed_at
      })),
      average: stats.average || 0,
      total: stats.total || 0
    });

  } catch (error) {
    console.error('Get user ratings error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get trips pending rating
app.get('/api/rating/pending', authenticateToken, async (req, res) => {
  try {
    // Find driver for this user
    const driver = await Driver.findOne({ user_id: req.user.userId });

    // Get trips where user participated but hasn't rated yet
    const trips = await Trip.find({
      $or: [
        { driver_id: driver?._id },
        { passenger_id: req.user.userId }
      ],
      status: 'completed'
    })
    .populate('passenger_id', 'name')
    .populate('driver_id')
    .sort({ completed_at: -1 });

    // Filter out trips already rated by this user
    const pendingTrips = [];
    for (const trip of trips) {
      const existingRating = await Rating.findOne({
        trip_id: trip._id,
        rater_id: req.user.userId
      });

      if (!existingRating) {
        const isDriver = driver && trip.driver_id._id.equals(driver._id);
        const otherUser = isDriver ? trip.passenger_id : await User.findById(trip.driver_id.user_id);

        pendingTrips.push({
          ...trip.toObject(),
          user_role: isDriver ? 'driver' : 'passenger',
          other_user_name: otherUser.name,
          other_user_id: otherUser._id
        });
      }
    }

    res.json(pendingTrips);

  } catch (error) {
    console.error('Get pending ratings error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Function to update user rating average
async function updateUserRating(userId, ratingType) {
  try {
    const ratingStats = await Rating.aggregate([
      {
        $match: {
          rated_id: new mongoose.Types.ObjectId(userId),
          rating_type: ratingType
        }
      },
      {
        $group: {
          _id: null,
          average: { $avg: '$rating' },
          total: { $sum: 1 }
        }
      }
    ]);

    const stats = ratingStats[0] || { average: 0, total: 0 };

    if (ratingType === 'passenger_to_driver') {
      // Update driver rating
      await Driver.updateOne(
        { user_id: userId },
        {
          rating_average: stats.average || 0,
          total_ratings: stats.total || 0
        }
      );
    }
    // For passenger ratings, we could add a separate field to users table

  } catch (error) {
    console.error('Update user rating error:', error);
  }
}

// Get active drivers (for passengers)
app.get('/api/active-drivers', authenticateToken, async (req, res) => {
  try {
    const drivers = await Driver.find({
      is_active: true,
      current_balance: { $gt: 0 }
    })
    .populate('user_id', 'name phone')
    .sort({ rating_average: -1, last_seen: -1 });

    const formattedDrivers = drivers.map(driver => ({
      id: driver._id,
      current_location: driver.current_location,
      is_active: driver.is_active,
      rating_average: driver.rating_average,
      total_ratings: driver.total_ratings,
      total_trips: driver.total_trips,
      name: driver.user_id.name,
      phone: driver.user_id.phone
    }));

    res.json(formattedDrivers);

  } catch (error) {
    console.error('Get active drivers error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get active passengers with trip requests (for drivers)
app.get('/api/active-passengers', authenticateToken, async (req, res) => {
  try {
    const passengers = await TripRequest.find({ status: 'pending' })
      .populate('passenger_id', 'name phone')
      .sort({ created_at: -1 });

    const formattedPassengers = passengers.map(request => ({
      ...request.toObject(),
      passenger_name: request.passenger_id.name,
      passenger_phone: request.passenger_id.phone
    }));

    res.json(formattedPassengers);

  } catch (error) {
    console.error('Get active passengers error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Update user location
app.post('/api/update-location', authenticateToken, async (req, res) => {
  try {
    const { location } = req.body;

    if (req.user.userType === 'driver') {
      await Driver.updateOne(
        { user_id: req.user.userId },
        {
          current_location: location,
          last_seen: new Date()
        }
      );
      res.json({ message: 'Location updated successfully' });
    } else {
      // For passengers, we could store in a separate table or just acknowledge
      res.json({ message: 'Location received' });
    }

  } catch (error) {
    console.error('Update location error:', error);
    res.status(500).json({ error: 'Location update failed' });
  }
});

// Socket.io connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  socket.on('join_room', (userType) => {
    socket.join(userType + '_room');
    console.log(`User joined ${userType} room`);
  });

  socket.on('driver_location_update', (data) => {
    socket.to('passenger_room').emit('driver_location_updated', data);
  });

  socket.on('passenger_trip_request', (data) => {
    socket.to('driver_room').emit('new_trip_request', data);
  });

  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  console.log(`🚀 MotoExpress server running on port ${PORT}`);
});

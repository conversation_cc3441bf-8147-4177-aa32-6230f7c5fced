import { useState, useEffect } from 'react';
import axios from 'axios';
import io from 'socket.io-client';

const API_URL = 'http://localhost:3001/api';
const socket = io('http://localhost:3001');

function ActiveUsers({ userType, onSelectUser }) {
  const [activeUsers, setActiveUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [userLocation, setUserLocation] = useState(null);

  useEffect(() => {
    // Get user's current location
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const location = `${position.coords.latitude},${position.coords.longitude}`;
          setUserLocation(location);
          updateLocation(location);
        },
        (error) => {
          console.error('Error getting location:', error);
          setUserLocation('Ubicación no disponible');
        }
      );
    }

    fetchActiveUsers();
    
    // Join socket room
    socket.emit('join_room', userType);

    // Socket listeners for real-time updates
    if (userType === 'passenger') {
      socket.on('driver_location_updated', (data) => {
        setActiveUsers(prev => 
          prev.map(driver => 
            driver.id === data.driverId 
              ? { ...driver, current_location: data.location }
              : driver
          )
        );
      });
    } else {
      socket.on('new_trip_request', (data) => {
        fetchActiveUsers(); // Refresh the list
      });
    }

    // Update location every 30 seconds for drivers
    let locationInterval;
    if (userType === 'driver' && userLocation) {
      locationInterval = setInterval(() => {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const location = `${position.coords.latitude},${position.coords.longitude}`;
              updateLocation(location);
              socket.emit('driver_location_update', {
                driverId: 'current_user',
                location: location
              });
            }
          );
        }
      }, 30000);
    }

    return () => {
      socket.off('driver_location_updated');
      socket.off('new_trip_request');
      if (locationInterval) clearInterval(locationInterval);
    };
  }, [userType, userLocation]);

  const fetchActiveUsers = async () => {
    try {
      const token = localStorage.getItem('token');
      const endpoint = userType === 'passenger' ? '/active-drivers' : '/active-passengers';
      
      const response = await axios.get(`${API_URL}${endpoint}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setActiveUsers(response.data);
    } catch (error) {
      console.error('Error fetching active users:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateLocation = async (location) => {
    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/update-location`, {
        location
      }, {
        headers: { Authorization: `Bearer ${token}` }
      });
    } catch (error) {
      console.error('Error updating location:', error);
    }
  };

  const getLocationName = (coordinates) => {
    if (!coordinates || coordinates === 'Ubicación no disponible') {
      return 'Ubicación no disponible';
    }
    
    // Simple location names based on coordinates (in a real app, use reverse geocoding)
    const [lat, lng] = coordinates.split(',').map(Number);
    
    if (lat > 9.0 && lat < 9.1 && lng > -79.6 && lng < -79.4) {
      return 'Casco Viejo';
    } else if (lat > 8.9 && lat < 9.0 && lng > -79.6 && lng < -79.4) {
      return 'Costa del Este';
    } else if (lat > 9.1 && lat < 9.2 && lng > -79.6 && lng < -79.4) {
      return 'San Miguelito';
    } else {
      return 'Ciudad de Panamá';
    }
  };

  if (loading) {
    return (
      <div className="active-users-loading">
        <div className="spinner"></div>
        <p>Buscando usuarios activos...</p>
      </div>
    );
  }

  return (
    <div className="active-users">
      <div className="location-status">
        <p>📍 Tu ubicación: {getLocationName(userLocation)}</p>
      </div>

      <h3>
        {userType === 'passenger' 
          ? `🏍️ Conductores Disponibles (${activeUsers.length})`
          : `👥 Pasajeros Solicitando Viaje (${activeUsers.length})`
        }
      </h3>

      {activeUsers.length === 0 ? (
        <div className="no-users">
          <p>
            {userType === 'passenger' 
              ? 'No hay conductores disponibles en este momento'
              : 'No hay solicitudes de viaje activas'
            }
          </p>
          <button onClick={fetchActiveUsers} className="btn-secondary">
            🔄 Actualizar
          </button>
        </div>
      ) : (
        <div className="users-list">
          {activeUsers.map(user => (
            <UserCard 
              key={user.id} 
              user={user} 
              userType={userType}
              onSelect={onSelectUser}
              getLocationName={getLocationName}
            />
          ))}
        </div>
      )}

      <button onClick={fetchActiveUsers} className="refresh-btn">
        🔄 Actualizar Lista
      </button>
    </div>
  );
}

function UserCard({ user, userType, onSelect, getLocationName }) {
  const handleSelect = () => {
    if (onSelect) {
      onSelect(user);
    }
  };

  if (userType === 'passenger') {
    // Driver card for passengers
    return (
      <div className="user-card driver-card" onClick={handleSelect}>
        <div className="user-info">
          <div className="user-header">
            <h4>🏍️ {user.name}</h4>
            <span className="status-badge active">Disponible</span>
          </div>
          <div className="driver-rating">
            {user.rating_average > 0 ? (
              <div className="rating-display">
                <span className="rating-stars">
                  {Array.from({ length: 5 }, (_, i) => (
                    <span key={i} className={`star ${i < Math.round(user.rating_average) ? 'filled' : ''}`}>
                      ⭐
                    </span>
                  ))}
                </span>
                <span className="rating-text">
                  {user.rating_average.toFixed(1)} ({user.total_ratings} calificaciones)
                </span>
              </div>
            ) : (
              <span className="no-rating">⭐ Nuevo conductor</span>
            )}
          </div>
          <p className="location">📍 {getLocationName(user.current_location)}</p>
          <p className="phone">📞 {user.phone}</p>
          {user.total_trips > 0 && (
            <p className="trips-count">🚗 {user.total_trips} viajes completados</p>
          )}
        </div>
        <div className="select-btn">
          <span>Seleccionar →</span>
        </div>
      </div>
    );
  } else {
    // Passenger card for drivers
    return (
      <div className="user-card passenger-card" onClick={handleSelect}>
        <div className="user-info">
          <div className="user-header">
            <h4>👤 {user.passenger_name}</h4>
            <span className="price-badge">${user.current_price}</span>
          </div>
          <div className="trip-route">
            <p><strong>📍 Desde:</strong> {user.pickup_location}</p>
            <p><strong>🎯 Hasta:</strong> {user.destination}</p>
            {user.estimated_distance && (
              <p><strong>📏 Distancia:</strong> {user.estimated_distance} km</p>
            )}
          </div>
          <div className="trip-details">
            <span className="payment-method">
              💳 {user.payment_method === 'yappy' ? 'Yappy' : 'Efectivo'}
            </span>
            {user.standard_price && (
              <span className="standard-price-badge">
                📊 Base: ${user.standard_price.toFixed(2)}
              </span>
            )}
            {user.price_increase > 0 && (
              <span className="price-increase-badge">
                ⬆️ +${user.price_increase.toFixed(2)}
              </span>
            )}
          </div>
        </div>
        <div className="select-btn">
          <span>Ver Detalles →</span>
        </div>
      </div>
    );
  }
}

export default ActiveUsers;
